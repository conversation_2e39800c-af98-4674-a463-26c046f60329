package com.ruoyi.knowledge.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * Milvus向量数据库配置类
 * 
 * <AUTHOR>
 * @date 2024-08-13
 */
@Configuration
@ConfigurationProperties(prefix = "knowledge.milvus")
public class MilvusConfig {

    /** Milvus服务器地址 */
    private String host = "localhost";

    /** Milvus服务器端口 */
    private int port = 19530;

    /** 数据库名称 */
    private String database = "default";

    /** 用户名 */
    private String username = "";

    /** 密码 */
    private String password = "";

    /** 是否使用TLS */
    private boolean secure = false;

    /** 连接超时时间（毫秒） */
    private long connectTimeoutMs = 10000;

    /** 保持连接时间（毫秒） */
    private long keepAliveTimeMs = 30000;

    /** 保持连接超时时间（毫秒） */
    private long keepAliveTimeoutMs = 5000;

    /** 是否保持连接 */
    private boolean keepAliveWithoutCalls = false;

    /** 重试次数 */
    private int retryTimes = 3;

    /** 默认集合名称前缀 */
    private String collectionPrefix = "knowledge_base_";

    /** 默认向量维度 */
    private int defaultDimension = 384;

    /** 默认索引类型 */
    private String defaultIndexType = "IVF_FLAT";

    /** 默认度量类型 */
    private String defaultMetricType = "L2";

    /** 索引参数 - nlist */
    private int indexNlist = 1024;

    /** 搜索参数 - nprobe */
    private int searchNprobe = 10;

    /** 分片数量 */
    private int shardsNum = 2;

    /** 副本数量 */
    private int replicasNum = 1;

    /** 是否自动创建集合 */
    private boolean autoCreateCollection = true;

    /** 是否自动创建索引 */
    private boolean autoCreateIndex = true;

    /** 是否自动加载集合 */
    private boolean autoLoadCollection = true;

    /** 批量插入大小 */
    private int batchSize = 1000;

    /** 是否启用一致性级别 */
    private String consistencyLevel = "Strong";

    public String getHost() {
        return host;
    }

    public void setHost(String host) {
        this.host = host;
    }

    public int getPort() {
        return port;
    }

    public void setPort(int port) {
        this.port = port;
    }

    public String getDatabase() {
        return database;
    }

    public void setDatabase(String database) {
        this.database = database;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public boolean isSecure() {
        return secure;
    }

    public void setSecure(boolean secure) {
        this.secure = secure;
    }

    public long getConnectTimeoutMs() {
        return connectTimeoutMs;
    }

    public void setConnectTimeoutMs(long connectTimeoutMs) {
        this.connectTimeoutMs = connectTimeoutMs;
    }

    public long getKeepAliveTimeMs() {
        return keepAliveTimeMs;
    }

    public void setKeepAliveTimeMs(long keepAliveTimeMs) {
        this.keepAliveTimeMs = keepAliveTimeMs;
    }

    public long getKeepAliveTimeoutMs() {
        return keepAliveTimeoutMs;
    }

    public void setKeepAliveTimeoutMs(long keepAliveTimeoutMs) {
        this.keepAliveTimeoutMs = keepAliveTimeoutMs;
    }

    public boolean isKeepAliveWithoutCalls() {
        return keepAliveWithoutCalls;
    }

    public void setKeepAliveWithoutCalls(boolean keepAliveWithoutCalls) {
        this.keepAliveWithoutCalls = keepAliveWithoutCalls;
    }

    public int getRetryTimes() {
        return retryTimes;
    }

    public void setRetryTimes(int retryTimes) {
        this.retryTimes = retryTimes;
    }

    public String getCollectionPrefix() {
        return collectionPrefix;
    }

    public void setCollectionPrefix(String collectionPrefix) {
        this.collectionPrefix = collectionPrefix;
    }

    public int getDefaultDimension() {
        return defaultDimension;
    }

    public void setDefaultDimension(int defaultDimension) {
        this.defaultDimension = defaultDimension;
    }

    public String getDefaultIndexType() {
        return defaultIndexType;
    }

    public void setDefaultIndexType(String defaultIndexType) {
        this.defaultIndexType = defaultIndexType;
    }

    public String getDefaultMetricType() {
        return defaultMetricType;
    }

    public void setDefaultMetricType(String defaultMetricType) {
        this.defaultMetricType = defaultMetricType;
    }

    public int getIndexNlist() {
        return indexNlist;
    }

    public void setIndexNlist(int indexNlist) {
        this.indexNlist = indexNlist;
    }

    public int getSearchNprobe() {
        return searchNprobe;
    }

    public void setSearchNprobe(int searchNprobe) {
        this.searchNprobe = searchNprobe;
    }

    public int getShardsNum() {
        return shardsNum;
    }

    public void setShardsNum(int shardsNum) {
        this.shardsNum = shardsNum;
    }

    public int getReplicasNum() {
        return replicasNum;
    }

    public void setReplicasNum(int replicasNum) {
        this.replicasNum = replicasNum;
    }

    public boolean isAutoCreateCollection() {
        return autoCreateCollection;
    }

    public void setAutoCreateCollection(boolean autoCreateCollection) {
        this.autoCreateCollection = autoCreateCollection;
    }

    public boolean isAutoCreateIndex() {
        return autoCreateIndex;
    }

    public void setAutoCreateIndex(boolean autoCreateIndex) {
        this.autoCreateIndex = autoCreateIndex;
    }

    public boolean isAutoLoadCollection() {
        return autoLoadCollection;
    }

    public void setAutoLoadCollection(boolean autoLoadCollection) {
        this.autoLoadCollection = autoLoadCollection;
    }

    public int getBatchSize() {
        return batchSize;
    }

    public void setBatchSize(int batchSize) {
        this.batchSize = batchSize;
    }

    public String getConsistencyLevel() {
        return consistencyLevel;
    }

    public void setConsistencyLevel(String consistencyLevel) {
        this.consistencyLevel = consistencyLevel;
    }

    /**
     * 获取完整的URI
     */
    public String getUri() {
        String protocol = secure ? "https" : "http";
        return String.format("%s://%s:%d", protocol, host, port);
    }

    /**
     * 根据知识库ID生成集合名称
     */
    public String getCollectionName(Long knowledgeBaseId) {
        return collectionPrefix + knowledgeBaseId;
    }

    /**
     * 检查是否需要认证
     */
    public boolean needsAuth() {
        return username != null && !username.trim().isEmpty();
    }
}
