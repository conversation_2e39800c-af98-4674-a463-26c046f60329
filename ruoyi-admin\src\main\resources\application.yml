# 项目相关配置
ruoyi:
  # 名称
  name: 智能知识管理系统
  # 版本
  version: 3.9.0
  # 版权年份
  copyrightYear: 2025
  # 文件路径 示例（ Windows配置D:/ruoyi/uploadPath，Linux配置 /home/<USER>/uploadPath）
  profile: D:/RuoYi-Vue/uploadPath
  # 获取ip地址开关
  addressEnabled: false
  # 验证码类型 math 数字计算 char 字符验证
  captchaType: char

# 开发环境配置
server:
  # 服务器的HTTP端口，默认为8080
  port: 8080
  servlet:
    # 应用的访问路径
    context-path: /
  tomcat:
    # tomcat的URI编码
    uri-encoding: UTF-8
    # 连接数满后的排队数，默认为100
    accept-count: 1000
    # 连接超时时间（毫秒）- 增加到3分钟
    connection-timeout: 180000
    threads:
      # tomcat最大线程数，默认为200
      max: 800
      # Tomcat启动初始化的线程数，默认值10
      min-spare: 100

# 短信服务配置
sms:
  provider: mock

# 日志配置
logging:
  level:
    com.ruoyi: debug
    org.springframework: warn

# 用户配置
user:
  password:
    # 密码最大错误次数
    maxRetryCount: 5
    # 密码锁定时间（默认10分钟）
    lockTime: 10

# Spring配置
spring:
  # 资源信息
  messages:
    # 国际化资源文件路径
    basename: i18n/messages
  profiles:
    active: druid
  # 文件上传
  servlet:
    multipart:
      # 单个文件大小
      max-file-size: 10MB
      # 设置总上传的文件大小
      max-request-size: 20MB
  # 服务模块
  devtools:
    restart:
      # 热部署开关
      enabled: true
  # redis 配置
  redis:
    # 地址
    host: localhost
    # 端口，默认为6379
    port: 6379
    # 数据库索引
    database: 0
    # 密码
    password:
    # 连接超时时间
    timeout: 10s
    lettuce:
      pool:
        # 连接池中的最小空闲连接
        min-idle: 0
        # 连接池中的最大空闲连接
        max-idle: 8
        # 连接池的最大数据库连接数
        max-active: 8
        # #连接池最大阻塞等待时间（使用负值表示没有限制）
        max-wait: -1ms

# token配置
token:
  # 令牌自定义标识
  header: Authorization
  # 令牌密钥
  secret: abcdefghijklmnopqrstuvwxyz
  # 令牌有效期（默认30分钟）
  expireTime: 30

# MyBatis配置
mybatis:
  # 搜索指定包别名
  typeAliasesPackage: com.ruoyi.**.domain
  # 配置mapper的扫描，找到所有的mapper.xml映射文件
  mapperLocations: classpath*:mapper/**/*Mapper.xml
  # 加载全局的配置文件
  configLocation: classpath:mybatis/mybatis-config.xml

# PageHelper分页插件
pagehelper:
  helperDialect: mysql
  supportMethodsArguments: true
  params: count=countSql

# Swagger配置
swagger:
  # 是否开启swagger
  enabled: true
  # 请求前缀
  pathMapping: /dev-api

# 防止XSS攻击
xss:
  # 过滤开关
  enabled: true
  # 排除链接（多个用逗号分隔）
  excludes: /system/notice,/ai/chat/stream
  # 匹配链接
  urlPatterns: /system/*,/monitor/*,/tool/*,/ai/*

# LangChain4j配置
langchain4j:
  open-ai:
    chat-model:
      base-url: https://dashscope.aliyuncs.com/compatible-mode/v1
      api-key: ${API-KEY}
      model-name: qwen-plus
      timeout: 150s
      max-tokens: 2000
      temperature: 0.7
      log-request: true
      log-response: true

# AI模块配置
ai:
  qwen:
    # API密钥（从环境变量获取）
    api-key: ${API-KEY}
    # 基础URL
    base-url: https://dashscope.aliyuncs.com/compatible-mode/v1
    # 默认模型
    model: qwen-plus
    # 超时时间（秒）- 增加到150秒
    timeout: 150
    # 最大Token数
    max-tokens: 2000
    # 温度参数
    temperature: 0.7

# 知识库配置
knowledge:
  embedding:
    store:
      # 存储类型: memory(内存)、redis(Redis) 或 milvus(Milvus)
      # 使用memory不需要额外配置，使用redis需要Redis Stack，使用milvus需要Milvus服务
      type: memory

  # Milvus向量数据库配置
  milvus:
    # 服务器地址
    host: localhost
    # 服务器端口
    port: 19530
    # 数据库名称
    database: default
    # 用户名（如果需要认证）
    username: ""
    # 密码（如果需要认证）
    password: ""
    # 是否使用TLS
    secure: false
    # 连接超时时间（毫秒）
    connect-timeout-ms: 10000
    # 保持连接时间（毫秒）
    keep-alive-time-ms: 30000
    # 保持连接超时时间（毫秒）
    keep-alive-timeout-ms: 5000
    # 是否保持连接
    keep-alive-without-calls: false
    # 重试次数
    retry-times: 3
    # 默认集合名称前缀
    collection-prefix: "knowledge_base_"
    # 默认向量维度（需要与嵌入模型维度一致）
    default-dimension: 384
    # 默认索引类型（IVF_FLAT, IVF_SQ8, IVF_PQ, HNSW, SCANN）
    default-index-type: "IVF_FLAT"
    # 默认度量类型（L2, IP, COSINE）
    default-metric-type: "L2"
    # 索引参数 - nlist
    index-nlist: 1024
    # 搜索参数 - nprobe
    search-nprobe: 10
    # 分片数量
    shards-num: 2
    # 副本数量
    replicas-num: 1
    # 是否自动创建集合
    auto-create-collection: true
    # 是否自动创建索引
    auto-create-index: true
    # 是否自动加载集合
    auto-load-collection: true
    # 批量插入大小
    batch-size: 1000
    # 一致性级别（Strong, Session, Bounded, Eventually）
    consistency-level: "Strong"

# OCR配置
ocr:
  tesseract:
    # Tesseract数据路径（可选，如果不配置将使用系统默认路径）
    # Windows示例: D:/tesseract/tessdata
    # Linux示例: /usr/share/tesseract-ocr/4.00/tessdata
    datapath: D:/tesseract/tessdata
    # 识别语言（支持多语言，用+分隔）
    # chi_sim: 中文简体, eng: 英文, chi_tra: 中文繁体
    language: chi_sim+eng
