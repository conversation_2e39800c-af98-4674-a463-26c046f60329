package com.ruoyi.knowledge.store;

import com.ruoyi.knowledge.config.MilvusConfig;
import dev.langchain4j.data.embedding.Embedding;
import dev.langchain4j.data.segment.TextSegment;
import dev.langchain4j.store.embedding.EmbeddingMatch;
import dev.langchain4j.store.embedding.EmbeddingSearchRequest;
import dev.langchain4j.store.embedding.EmbeddingSearchResult;
import dev.langchain4j.store.embedding.EmbeddingStore;
import dev.langchain4j.store.embedding.milvus.MilvusEmbeddingStore;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 自定义Milvus向量存储实现
 * 支持多知识库的集合管理
 * 
 * <AUTHOR>
 * @date 2024-08-13
 */
@Component
public class CustomMilvusEmbeddingStore implements EmbeddingStore<TextSegment> {

    private static final Logger logger = LoggerFactory.getLogger(CustomMilvusEmbeddingStore.class);

    @Autowired(required = false)
    private MilvusConfig milvusConfig;

    /** 缓存不同知识库对应的MilvusEmbeddingStore实例 */
    private final Map<Long, MilvusEmbeddingStore> storeCache = new ConcurrentHashMap<>();

    /** 默认的MilvusEmbeddingStore实例（用于全局搜索） */
    private MilvusEmbeddingStore defaultStore;

    @PostConstruct
    public void init() {
        if (milvusConfig == null) {
            logger.warn("Milvus配置未找到，CustomMilvusEmbeddingStore将不可用");
            return;
        }

        logger.info("初始化CustomMilvusEmbeddingStore，Milvus地址: {}", milvusConfig.getUri());

        // 创建默认的存储实例
        try {
            defaultStore = createMilvusStore("default_collection");
            logger.info("默认Milvus存储实例创建成功");
        } catch (Exception e) {
            logger.warn("创建默认Milvus存储实例失败，Milvus功能将不可用: {}", e.getMessage());
            defaultStore = null;
        }
    }

    /**
     * 获取指定知识库的MilvusEmbeddingStore实例
     */
    public MilvusEmbeddingStore getStoreForKnowledgeBase(Long knowledgeBaseId) {
        return storeCache.computeIfAbsent(knowledgeBaseId, id -> {
            try {
                String collectionName = milvusConfig.getCollectionName(id);
                logger.info("为知识库 {} 创建Milvus存储实例，集合名称: {}", id, collectionName);
                return createMilvusStore(collectionName);
            } catch (Exception e) {
                logger.error("为知识库 {} 创建Milvus存储实例失败: {}", id, e.getMessage(), e);
                return defaultStore; // 失败时返回默认实例
            }
        });
    }

    /**
     * 创建MilvusEmbeddingStore实例
     */
    private MilvusEmbeddingStore createMilvusStore(String collectionName) {
        MilvusEmbeddingStore.Builder builder = MilvusEmbeddingStore.builder()
                .uri(milvusConfig.getUri())
                .collectionName(collectionName)
                .dimension(milvusConfig.getDefaultDimension())
                .autoFlushOnInsert(true);

        // 如果需要认证
        if (milvusConfig.needsAuth()) {
            builder.username(milvusConfig.getUsername())
                   .password(milvusConfig.getPassword());
        }

        // 如果指定了数据库
        if (milvusConfig.getDatabase() != null && !milvusConfig.getDatabase().equals("default")) {
            builder.databaseName(milvusConfig.getDatabase());
        }

        return builder.build();
    }

    @Override
    public String add(Embedding embedding) {
        if (defaultStore == null) {
            throw new RuntimeException("默认Milvus存储实例未初始化");
        }
        return defaultStore.add(embedding);
    }

    @Override
    public void add(String id, Embedding embedding) {
        if (defaultStore == null) {
            throw new RuntimeException("默认Milvus存储实例未初始化");
        }
        defaultStore.add(id, embedding);
    }

    @Override
    public String add(Embedding embedding, TextSegment textSegment) {
        // 从元数据中获取知识库ID
        String knowledgeBaseIdStr = textSegment.metadata().getString("knowledgeBaseId");
        if (knowledgeBaseIdStr != null) {
            try {
                Long knowledgeBaseId = Long.parseLong(knowledgeBaseIdStr);
                MilvusEmbeddingStore store = getStoreForKnowledgeBase(knowledgeBaseId);
                return store.add(embedding, textSegment);
            } catch (NumberFormatException e) {
                logger.warn("无效的知识库ID: {}, 使用默认存储", knowledgeBaseIdStr);
                if (defaultStore != null) {
                    return defaultStore.add(embedding, textSegment);
                }
            }
        } else {
            logger.warn("文本段落缺少知识库ID元数据，使用默认存储");
            if (defaultStore != null) {
                return defaultStore.add(embedding, textSegment);
            }
        }
        return null; // 如果所有存储都不可用
    }



    @Override
    public List<String> addAll(List<Embedding> embeddings) {
        if (defaultStore == null) {
            throw new RuntimeException("默认Milvus存储实例未初始化");
        }
        return defaultStore.addAll(embeddings);
    }

    @Override
    public List<String> addAll(List<Embedding> embeddings, List<TextSegment> textSegments) {
        if (embeddings.size() != textSegments.size()) {
            throw new IllegalArgumentException("嵌入向量和文本段落数量不匹配");
        }

        List<String> allIds = new java.util.ArrayList<>();

        // 按知识库ID分组
        Map<Long, List<Integer>> groupByKnowledgeBase = new ConcurrentHashMap<>();

        for (int i = 0; i < textSegments.size(); i++) {
            TextSegment segment = textSegments.get(i);
            String knowledgeBaseIdStr = segment.metadata().getString("knowledgeBaseId");

            if (knowledgeBaseIdStr != null) {
                try {
                    Long knowledgeBaseId = Long.parseLong(knowledgeBaseIdStr);
                    groupByKnowledgeBase.computeIfAbsent(knowledgeBaseId, k -> new java.util.ArrayList<>()).add(i);
                } catch (NumberFormatException e) {
                    logger.warn("无效的知识库ID: {}", knowledgeBaseIdStr);
                }
            }
        }

        // 分别向不同的知识库存储
        for (Map.Entry<Long, List<Integer>> entry : groupByKnowledgeBase.entrySet()) {
            Long knowledgeBaseId = entry.getKey();
            List<Integer> indices = entry.getValue();

            List<Embedding> batchEmbeddings = new java.util.ArrayList<>();
            List<TextSegment> batchSegments = new java.util.ArrayList<>();

            for (Integer index : indices) {
                batchEmbeddings.add(embeddings.get(index));
                batchSegments.add(textSegments.get(index));
            }

            MilvusEmbeddingStore store = getStoreForKnowledgeBase(knowledgeBaseId);
            List<String> batchIds = store.addAll(batchEmbeddings, batchSegments);
            if (batchIds != null) {
                allIds.addAll(batchIds);
            }

            logger.info("向知识库 {} 批量添加 {} 个向量", knowledgeBaseId, batchEmbeddings.size());
        }

        return allIds;
    }

    @Override
    public EmbeddingSearchResult<TextSegment> search(EmbeddingSearchRequest embeddingSearchRequest) {
        if (defaultStore == null) {
            throw new RuntimeException("默认Milvus存储实例未初始化");
        }
        return defaultStore.search(embeddingSearchRequest);
    }

    /**
     * 在指定知识库中搜索
     */
    public EmbeddingSearchResult<TextSegment> searchInKnowledgeBase(Long knowledgeBaseId, EmbeddingSearchRequest embeddingSearchRequest) {
        MilvusEmbeddingStore store = getStoreForKnowledgeBase(knowledgeBaseId);
        return store.search(embeddingSearchRequest);
    }

    /**
     * 删除指定知识库的集合
     */
    public void dropKnowledgeBaseCollection(Long knowledgeBaseId) {
        try {
            MilvusEmbeddingStore store = storeCache.remove(knowledgeBaseId);
            if (store != null) {
                // 注意：LangChain4j的MilvusEmbeddingStore可能没有直接的删除集合方法
                // 这里我们只是从缓存中移除，实际的集合删除需要通过Milvus客户端进行
                logger.info("从缓存中移除知识库 {} 的存储实例", knowledgeBaseId);
            }
        } catch (Exception e) {
            logger.error("删除知识库 {} 的集合失败: {}", knowledgeBaseId, e.getMessage(), e);
        }
    }

    /**
     * 获取缓存的存储实例数量
     */
    public int getCachedStoreCount() {
        return storeCache.size();
    }

    /**
     * 清理缓存
     */
    public void clearCache() {
        storeCache.clear();
        logger.info("清理Milvus存储实例缓存");
    }
}
